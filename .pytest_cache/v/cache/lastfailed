{"src/test/config/endpoint_test.py::TestConfigEndpoint::test_s3_endpoint": true, "src/test/reader/mssql_reader_test.py::TestMysqlReader::test_spark_mysql_full": true, "src/test/reader/mssql_reader_test.py::TestMysqlReader::test_spark_mssql_predicate": true, "src/test/reader/s3_reader_test.py::TestMysqlReader::test_spark_s3_full": true, "src/test/utils/delta_bound_util_test.py::TestDeltaBoundUtil::test_lower_bound_retrieve_postgre_first_time": true, "src/test/utils/delta_bound_util_test.py::TestDeltaBoundUtil::test_lower_bound_retrieve_postgre_second_time": true, "src/test/writer/writer_test.py::TestWriter::test_write_to_s3": true}