from writer.writer import Writer
from config.writer import IcebergWriterConfig
from pyspark.sql.dataframe import DataFrame
from pyspark.sql.functions import current_timestamp
from typing import Optional
import logging


class IcebergWriter(Writer):
    def __init__(self, config: IcebergWriterConfig, process_date: str, custom_options: Optional[dict[str, str]]):
        self.config = config
        self.custom_options = custom_options
        self.endpoint = config.endpoint
        self.process_date = process_date
        
    def write(self, df: DataFrame):
        """
        Write DataFrame to Apache Iceberg table.
        
        Iceberg provides ACID transactions, schema evolution, time travel,
        and efficient updates/deletes for large analytical datasets.
        """
        endpoint_config = self.config.endpoint_config
        
        # Configure Spark for Iceberg
        spark = df.sparkSession
        self._configure_iceberg_spark(spark, endpoint_config)
        
        # Build the full table identifier
        table_name = self.config.table_name
        
        # Get write mode
        mode = self.config.mode.lower()
        
        # Start building the write operation
        writer = df.write.format("iceberg")
        
        # Set write mode
        if mode == "append":
            writer = writer.mode("append")
        elif mode == "overwrite":
            writer = writer.mode("overwrite")
        elif mode == "merge":
            # For merge operations, we'll use Iceberg's MERGE INTO SQL
            self._perform_merge_operation(df, table_name)
            return
        else:
            raise ValueError(f"Unsupported write mode for Iceberg: {mode}")
        
        # Add partition columns if specified
        if self.config.partition_columns:
            writer = writer.partitionBy(*self.config.partition_columns)
        
        # Add sort columns for table optimization
        if self.config.sort_columns:
            writer = writer.sortBy(*self.config.sort_columns)
        
        # Add custom table properties
        if self.config.table_properties:
            for key, value in self.config.table_properties.items():
                writer = writer.option(f"write.metadata.{key}", value)
        
        # Add custom options from config
        if self.custom_options:
            for key, value in self.custom_options.items():
                writer = writer.option(key, value)
        
        # Write to Iceberg table
        logging.info(f"Writing DataFrame to Iceberg table: {table_name}")
        writer.saveAsTable(table_name)
        
        logging.info(f"Successfully wrote data to Iceberg table: {table_name}")
    
    def _configure_iceberg_spark(self, spark, endpoint_config):
        """Configure Spark session for Iceberg operations."""
        
        # Get Spark configuration
        spark_conf = spark.sparkContext.getConf()
        
        # Configure Iceberg catalog
        catalog_name = "spark_catalog"  # Default catalog name
        
        # Set Iceberg catalog implementation
        spark_conf.set(f"spark.sql.catalog.{catalog_name}", "org.apache.iceberg.spark.SparkCatalog")
        
        # Configure storage backend (S3 in this case)
        if endpoint_config and hasattr(endpoint_config, 'bucket'):
            warehouse_path = f"s3a://{endpoint_config.bucket}/iceberg-warehouse"
            spark_conf.set(f"spark.sql.catalog.{catalog_name}.warehouse", warehouse_path)
            spark_conf.set(f"spark.sql.catalog.{catalog_name}.catalog-impl", "org.apache.iceberg.aws.glue.GlueCatalog")
            
            # Configure S3 credentials if available
            if endpoint_config.authentication and endpoint_config.authentication.credential:
                creds = endpoint_config.authentication.credential
                hadoop_config = spark.sparkContext._jsc.hadoopConfiguration()
                hadoop_config.set("spark.hadoop.fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")
                hadoop_config.set("spark.hadoop.fs.s3a.aws.credentials.provider", 
                                "org.apache.hadoop.fs.s3a.TemporaryAWSCredentialsProvider")
                hadoop_config.set("spark.hadoop.fs.s3a.access.key", creds.access_key_id)
                hadoop_config.set("spark.hadoop.fs.s3a.secret.key", creds.secret_access_key)
                if hasattr(creds, 'session_token') and creds.session_token:
                    hadoop_config.set("spark.hadoop.fs.s3a.session.token", creds.session_token)
        else:
            # Use local filesystem for testing
            warehouse_path = "/tmp/iceberg-warehouse"
            spark_conf.set(f"spark.sql.catalog.{catalog_name}.warehouse", warehouse_path)
            spark_conf.set(f"spark.sql.catalog.{catalog_name}.catalog-impl", "org.apache.iceberg.hadoop.HadoopCatalog")
        
        # Enable Iceberg SQL extensions
        spark_conf.set("spark.sql.extensions", "org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions")
        
        logging.info(f"Configured Iceberg catalog with warehouse: {warehouse_path}")
    
    def _perform_merge_operation(self, df: DataFrame, table_name: str):
        """
        Perform MERGE operation using Iceberg's MERGE INTO SQL.
        This is useful for upsert operations (insert new records, update existing ones).
        """
        spark = df.sparkSession
        
        # Create a temporary view for the source data
        temp_view_name = f"temp_source_{table_name.replace('.', '_')}"
        df.createOrReplaceTempView(temp_view_name)
        
        # Example MERGE SQL - you would customize this based on your needs
        merge_sql = f"""
        MERGE INTO {table_name} AS target
        USING {temp_view_name} AS source
        ON target.id = source.id
        WHEN MATCHED THEN UPDATE SET *
        WHEN NOT MATCHED THEN INSERT *
        """
        
        logging.info(f"Executing MERGE operation: {merge_sql}")
        spark.sql(merge_sql)
        
        # Clean up temporary view
        spark.catalog.dropTempView(temp_view_name)
