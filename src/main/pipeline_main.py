from dataclasses import dataclass
from pyspark import SparkConf, SparkContext
from pyspark.sql import SparkSession
from utils.constants import INTERNAL_PIPELINE_ENGINE_ENDPOINT
from pipeline_factory import PipelineFactory
from config.pipeline import PipelineConfig
from reader.reader import Reader
from reader.mysql_reader import MysqlReader
from reader.mssql_reader import MssqlReader
from utils.delta_bound_util import ValueSet,append
from utils.commons import JobParams,Pipeline
from datetime import datetime
import logging
import argparse
import json
import os
import random
import string


    
class PipelineMain:
    def __init__(self) :
        job_params = self._parse_command_args() 
        extra_jars_folder = f"{job_params.extra_jars_folder}/*"        
        spark_config = SparkConf()
        spark_config \
                .set("mapreduce.filteroutpitcommitter.marksuccessfuljobs","false") \
                .set("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
                .set("spark.scheduler.mode", "FAIR") \
                .set("spark.sql.legacy.charVarcharAsString","true") \
                .set("spark.driver.extraClassPath", extra_jars_folder) \
                .set("spark.executor.extraClassPath", extra_jars_folder) \
                .set("spark.hadoop.fs.s3a.connection.timeout", "5000") \
                .set("spark.hadoop.fs.s3a.attempts.maximum", "3")
        
        ## Amazon S3 on AWS cloud
        if job_params.s3_type == "aws":
            spark_config \
                    .set("spark.hadoop.fs.s3a.path.style.access","false")
        else:
            if not job_params.s3_url:
                raise ValueError("Param s3_url must be provide because using self-host S3 compatible")   
            else:
                spark_config \
                        .set("spark.hadoop.fs.s3a.path.style.access","true") \
                        .set("spark.hadoop.fs.s3a.endpoint",job_params.s3_url)    
        ############ apply root dir for jobs and endpoint
        root_dir = job_params.config_root_dir.removesuffix("/") # type: ignore        
        if root_dir == "s3":
            job_root_dir = f"s3a://{root_dir}/jobs"
            endpoint_root_dir = f"s3a://{root_dir}/endpoints"
        else:
            job_root_dir = f"{root_dir}/jobs"
            endpoint_root_dir = f"{root_dir}/endpoints" 
        os.environ["CONFIG_ENDPOINTS_DIR"]=endpoint_root_dir
        internal_endpoint_path = f"{endpoint_root_dir}/{INTERNAL_PIPELINE_ENGINE_ENDPOINT}.yaml"
        
        job_id = ''.join(random.choices(string.ascii_letters + string.digits, k=10)) + datetime.now().strftime("%Y%m%d%H%M%S")
        print(f"job is: {job_id}")
        spark = SparkSession.Builder() \
                        .appName(job_params.job_id if job_params.job_id else job_id) \
                        .config(conf=spark_config) \
                        .getOrCreate()         
        pipeline = PipelineFactory(job_params)
        pipeline_list_config = pipeline.list_pipeline
        self._fetch_config(job_params, spark.sparkContext, pipeline_list_config,job_root_dir,endpoint_root_dir)
        job_list = pipeline.list_pipeline        
        if job_params.app_type == "batch":
            
            self._run_batch(spark,job_list,internal_endpoint_path) 
        else:
            spark.sparkContext.stop()
      
    def _fetch_config(self,job_params: JobParams, sc: SparkContext, pipeline_list_config: list[PipelineConfig], job_root_dir: str, endpoint_root_dir: str):
        config_file = f"{job_root_dir}/{job_params.config_file_path}"
        sc.addFile(config_file)
        if job_params.app_type == "batch":
            endpoint_set = {INTERNAL_PIPELINE_ENGINE_ENDPOINT}
        else:
            endpoint_set: set[str] = set()
        for item in (pipeline_list_config):
            if not item.readers.endpoint.strip():
                endpoint_set.add(item.readers.endpoint)
            if not item.writers.endpoint.strip():
                endpoint_set.add(item.writers.endpoint)
        
        # loop through each endpoint then add to spark context
        for endpoint in endpoint_set:
            sc.addFile(f"{endpoint_root_dir}/{endpoint}.yaml")
            
    def _run_batch(self,spark: SparkSession, job_list: list[Pipeline], internal_endpoint_path: str):
        for pipeline in job_list:        
            try:
                input_df = pipeline.readers.read(spark)
                for index,transformer in enumerate(pipeline.transformers): # type: ignore
                    # first will transfor the input_df
                    if index == 0:
                        transformed_df = transformer.transform(input_df)
                    # the rest will transform from transformed_df
                    else:
                        transformed_df = transformer.transform(transformed_df) # type: ignore
                pipeline.writers.write(transformed_df) # type: ignore
                self._collect_job_statistics(pipeline.readers,transformed_df.count(),internal_endpoint_path) # type: ignore
            except Exception as e:
                logging.error(f"Unexpected error happended: {e}")
            
    def _collect_job_statistics(self, reader: Reader, count: int, internal_endpoint_path: str):
        if isinstance(reader, MysqlReader):
            if reader.strategy == "delta":
                conn_props = reader.jdbc_config
                to_be_updated_values = ValueSet(
                    conn_props.system,
                    conn_props.schema,
                    conn_props.table,
                    reader.lower_bound,
                    reader.upper_bound,
                    count
                    )
                append(to_be_updated_values,internal_endpoint_path)
        elif isinstance(reader,MssqlReader):
            if reader.strategy == "delta":
                conn_props = reader.jdbc_config
                to_be_updated_values = ValueSet(
                    conn_props.system,
                    conn_props.schema,
                    conn_props.table,
                    reader.lower_bound,
                    reader.upper_bound,
                    count
                    )
                append(to_be_updated_values,internal_endpoint_path)
    
    def _parse_command_args(self) -> JobParams:
        parser = argparse.ArgumentParser(description="Spark Pipeline for Data Platform")
        parser.add_argument("--config_file", type=str, required=True, help="Name of the job config file that is required to execute the job")
        parser.add_argument("--config_source_type", type=str, help="s3 or local")
        parser.add_argument("--config_root_dir", type=str, help="Bucket name the job config located")
        parser.add_argument("--app_type",required=True, type=str, help="batch or stream")
        parser.add_argument("--lowerbound", type=str, help="String for datetime format of lowerbound")
        parser.add_argument("--upperbound", type=str, help="String for datetime format of upperbound")
        parser.add_argument("--job_id", type=str, help="String for job_id")
        parser.add_argument("--report_date", type=str, help="String for output date")
        parser.add_argument("--pipeline", type=lambda s: s.split(","), help="Job name need to be run")
        parser.add_argument("--batch_size", type=str,default=1, help="Batch size for runing parallel")
        parser.add_argument("--custom", type=json.loads, help="Custom options")
        parser.add_argument("--predicate", type=str, help="Predicate for filtering")
        parser.add_argument("--s3_type",required=True, type=str,default="aws", help="Amazon S3 or S3 self-host")
        parser.add_argument("--s3_url", type=str, help="Amazon S3 URL")
        parser.add_argument("--extra_jars_folder", type=str, help="Extra Jars Folder")
        args = parser.parse_args()                             
        return JobParams(args.config_file,args.config_source_type,args.config_root_dir,args.app_type,args.lowerbound,args.upperbound,args.report_date,args.pipeline,args.batch_size,args.custom,args.predicate,args.s3_type,args.job_id,args.s3_url,args.extra_jars_folder)
    
if __name__ == "__main__":
    pipeline = PipelineMain()