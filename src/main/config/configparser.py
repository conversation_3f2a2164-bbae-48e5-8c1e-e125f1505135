from typing import TypeVar, Type
from pydantic import BaseModel,ValidationError
import yaml
import logging

T = TypeVar("T", bound=BaseModel)

def load_yaml(data_yaml: str, model: Type[T]) :
    """Load YAML file and parse it into a given Pydantic model."""
    data = yaml.safe_load(data_yaml)
    
    try:
        model_data = model(**data)
        return         model_data 
    except Exception as e:
        logging.error(f"Unexpected error happended: {e}")
        raise

