# Example Iceberg Writer Configuration
# This shows how to configure writing data to Apache Iceberg tables

type: IcebergWriter
endpoint: s3.localstack.endpoint.yaml  # S3 endpoint for Iceberg storage
table_name: my_database.customer_data  # Fully qualified table name
mode: append  # append, overwrite, or merge
partition_columns:
  - year
  - month
sort_columns:  # Optional: columns to sort by for better query performance
  - customer_id
  - created_at
table_properties:  # Optional: Iceberg table properties
  write.format.default: parquet
  write.parquet.compression-codec: snappy
  history.expire.max-snapshot-age-ms: "604800000"  # 7 days
