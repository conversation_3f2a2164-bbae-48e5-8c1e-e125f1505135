jobs:
  - name: iceberg_example_job
    readers:
      type: S3Reader
      endpoint: s3.localstack.endpoint.yaml
      key: sample-data/customers
      format: 
        type: ParquetReaderFormat
        source_options:
          inferSchema: 'true'
    transformers:
       - type: WithColumnTransformer
         columns:
           - colname: processed_date
             expr: current_date()
           - colname: age_group
             expr: CASE WHEN age < 30 THEN 'young' WHEN age < 50 THEN 'middle' ELSE 'senior' END
       - type: ProjectionTransformer
         columns: 
           - id
           - name  
           - age
           - age_group
           - processed_date
    writers:
      type: S3Writer
      endpoint: s3.localstack.endpoint.yaml
      key: iceberg-warehouse/customers_table
      mode: overwrite
      format: 
        type: IcebergWriterFormat
      table_name: customers_iceberg
      partition_columns:
        - age_group
      sort_columns:
        - id
      table_properties:
        write.format.default: parquet
        write.parquet.compression-codec: snappy
