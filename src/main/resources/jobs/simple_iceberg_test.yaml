jobs:
  - name: simple_iceberg_test
    readers:
      type: S3Reader
      endpoint: s3.localstack.endpoint.yaml
      key: test-data/sample.json
      format: 
        type: JsonReaderFormat
        source_options:
          inferSchema: 'true'
    transformers:
       - type: WithColumnTransformer
         columns:
           - colname: test_timestamp
             expr: current_timestamp()
    writers:
      type: IcebergWriter
      endpoint: s3.localstack.endpoint.yaml
      table_name: simple_test_table
      mode: overwrite
