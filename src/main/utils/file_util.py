import logging

def read_file(file_path: str):
    logging.info(f"Reading file {file_path}")
    content =""
    try:
        # Attempt to open and read the file as a string
        with open(file_path, "r") as file:
            content = file.read()
        return content
    except FileNotFoundError:
        logging.error(f"Error: The file {file_path} does not exist.")
        raise FileNotFoundError
    except PermissionError:
        logging.error(f"Error: You don't have permission to access {file_path}.")
    except Exception as e:
        logging.error(f"An unexpected error occurred: {e}")
    finally:
        return content