from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import logging

logging.basicConfig()
logging.getLogger("sqlalchemy.engine").setLevel(logging.INFO)
class JdbcConnect:
    
    def __init__(self,
        db_type: str,
        username: str,
        password: str,
        host: str,
        database: str):
        self.engine = self.get_engine(db_type, username, password, host, database)
    
    @staticmethod
    def get_engine(
        db_type: str,
        username: str,
        password: str,
        host: str,
        database: str
    ) :
        
        db_type = db_type.lower()
        if db_type == "mysql":
            return create_engine(f"mysql+pymysql://{username}:{password}@{host}/{database}")
        elif db_type == 'mssql':
            return create_engine(f"mssql+pyodbc://{username}:{password}@{host}/{database}?driver=ODBC+Driver+17+for+SQL+Server",
                                connect_args={"autocommit": True})
        elif db_type == 'postgresql':
            return create_engine(f"postgresql+psycopg2://{username}:{password}@{host}/{database}")
        else:
            raise ValueError(f"Unsupported database type: {db_type}")
        
    def run_query(self, query):
        with self.engine.connect() as conn:                  
            try:
                result = conn.execute(text(query)).fetchone()
                conn.commit() # type: ignore
                return result
            except:
                raise
            finally:
                conn.close()
        