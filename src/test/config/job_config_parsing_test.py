from config.configparser import load_yaml
from config.pipeline import PipelineListConfig
from config.reader import ReaderConfig
from config.transformer import TransformerConfig
from config.writer import WriterConfig
from utils.file_util import read_file
import os

class TestJobConfigParsing:
    def test_job_config_parsing_multi(self):
        os.environ["CONFIG_ENDPOINTS_DIR"] = "/Users/<USER>/Documents/programming/de/spark-data-loader/src/test/resources"
        file_path = "src/test/resources/job.sample.yaml"    
        string_yaml= read_file(file_path)
        config = load_yaml(string_yaml,PipelineListConfig)                    
        first_job = config.jobs[0]
        second_job =config.jobs[1]
        assert first_job.name =="job1"
        assert second_job.name =="job2"
        assert isinstance(first_job.readers,ReaderConfig)
        assert isinstance(first_job.transformers[0],TransformerConfig) # type: ignore
        assert isinstance(first_job.writers,WriterConfig)
        assert isinstance(second_job.readers,ReaderConfig)
        assert isinstance(second_job.transformers[0],TransformerConfig) # type: ignore
        assert isinstance(second_job.writers,WriterConfig)
        
        