import pytest
import os
from config.writer import IcebergWriterConfig
from writer.iceberg_writer import IcebergWriter
from pyspark.sql import SparkSession
from pyspark.sql.types import StructType, StructField, StringType, IntegerType, TimestampType
from datetime import datetime


class TestIcebergWriter:
    
    def test_iceberg_writer_config_creation(self):
        """Test that IcebergWriterConfig can be created properly."""
        os.environ["CONFIG_ENDPOINTS_DIR"] = "src/test/resources"
        
        config = IcebergWriterConfig(
            type="IcebergWriter",
            endpoint="s3.localstack.endpoint.yaml",
            table_name="test_db.customers",
            mode="append",
            partition_columns=["year", "month"],
            sort_columns=["id"],
            table_properties={"write.format.default": "parquet"}
        )
        
        assert config.type == "IcebergWriter"
        assert config.table_name == "test_db.customers"
        assert config.mode == "append"
        assert config.partition_columns == ["year", "month"]
        assert config.sort_columns == ["id"]
        assert config.table_properties["write.format.default"] == "parquet"
    
    def test_iceberg_writer_initialization(self):
        """Test that IcebergWriter can be initialized."""
        os.environ["CONFIG_ENDPOINTS_DIR"] = "src/test/resources"
        
        config = IcebergWriterConfig(
            type="IcebergWriter",
            endpoint="s3.localstack.endpoint.yaml",
            table_name="test_db.customers",
            mode="append"
        )
        
        writer = IcebergWriter(config, "2025-01-01", None)
        
        assert writer.config == config
        assert writer.process_date == "2025-01-01"
        assert writer.custom_options is None
    
    @pytest.mark.skip(reason="Requires Iceberg JAR and proper Spark configuration")
    def test_iceberg_writer_write_operation(self, spark_setup):
        """
        Test actual write operation to Iceberg table.
        This test is skipped by default as it requires:
        1. Iceberg JAR files to be present
        2. Proper Spark configuration for Iceberg
        3. S3 or local storage setup
        """
        os.environ["CONFIG_ENDPOINTS_DIR"] = "src/test/resources"
        
        # Create test configuration
        config = IcebergWriterConfig(
            type="IcebergWriter",
            endpoint="s3.localstack.endpoint.yaml",
            table_name="test_db.test_customers",
            mode="overwrite",
            partition_columns=["year"]
        )
        
        # Create test DataFrame
        schema = StructType([
            StructField("id", IntegerType(), True),
            StructField("name", StringType(), True),
            StructField("age", IntegerType(), True),
            StructField("year", IntegerType(), True),
            StructField("created_at", TimestampType(), True)
        ])
        
        test_data = [
            (1, "Alice", 30, 2025, datetime(2025, 1, 1)),
            (2, "Bob", 25, 2025, datetime(2025, 1, 2)),
            (3, "Charlie", 35, 2024, datetime(2024, 12, 31))
        ]
        
        df = spark_setup.createDataFrame(test_data, schema)
        
        # Create writer and write data
        writer = IcebergWriter(config, "2025-01-01", None)
        
        # This would write to Iceberg table
        # writer.write(df)
        
        # Verify table was created and data was written
        # result_df = spark_setup.sql(f"SELECT * FROM {config.table_name}")
        # assert result_df.count() == 3
        
        # Test that we can read the data back
        # alice_record = spark_setup.sql(f"SELECT * FROM {config.table_name} WHERE name = 'Alice'").collect()
        # assert len(alice_record) == 1
        # assert alice_record[0]['age'] == 30
