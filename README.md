# Testing Setup Guide for spark-loader

This guide will help you set up the testing environment for the spark-loader project.

## Prerequisites

### 1. Install ODBC Drivers (macOS)

The MSSQL tests require ODBC drivers to be installed. Run these commands:

```bash
# Install unixODBC via Homebrew
brew install unixodbc

# Install Microsoft ODBC Driver for SQL Server
brew tap microsoft/mssql-release https://github.com/Microsoft/homebrew-mssql-release
brew install msodbcsql18 mssql-tools18

# Verify installation
odbcinst -q -d
```

**Note**: The installation will prompt you to accept Microsoft's license terms. Enter `yes` when prompted.

### 2. Install Spark Dependencies

The project requires Spark with S3 support. Download the required JAR files using the provided script:

```bash
# Make the download script executable and run it
chmod +x download_jars.sh
./download_jars.sh
```

This will download all required JAR dependencies to `src/main/resources/jars/`:
- AWS SDK Bundle for S3 integration
- Hadoop AWS libraries
- Database drivers (MySQL, PostgreSQL, SQL Server)

### 3. Start Test Services

```bash
# Start all required services
docker-compose up -d

# Verify services are running
docker-compose ps
```

### 4. Install Python Dependencies

```bash
# Install Python dependencies
pip install -r requirements.txt
```

## Running Tests

### Run All Tests

```bash
pytest -v
```

**Expected Result**: All 16 tests should pass (100% success rate)

### Run Specific Test Categories

```bash
# Configuration tests (4 tests)
pytest src/test/config/ -v

# Reader tests (5 tests) - includes MySQL, MSSQL, and S3
pytest src/test/reader/ -v

# Writer tests (2 tests) - PostgreSQL and S3
pytest src/test/writer/ -v

# Transformer tests (1 test)
pytest src/test/transformer/ -v

# Utility tests (3 tests) - Delta bound calculations
pytest src/test/utils/ -v

# Job configuration tests (1 test)
pytest src/test/config/job_config_parsing_test.py -v
```

## Verification Commands

### Check ODBC Installation
```bash
# List installed ODBC drivers
odbcinst -q -d

# Should show: [ODBC Driver 18 for SQL Server]
```

### Check Docker Services
```bash
# Verify all services are running
docker-compose ps

# Check service health
curl http://localhost:4566/health  # LocalStack S3
```

### Check JAR Dependencies
```bash
# List downloaded JAR files
ls -la src/main/resources/jars/

# Should include:
# - aws-java-sdk-bundle-1.12.262.jar
# - hadoop-aws-3.3.4.jar
# - mysql-connector-java-8.0.33.jar
# - postgresql-42.6.0.jar
# - mssql-jdbc-12.4.1.jre11.jar
```

## Test Structure

The tests are organized as follows:
- `src/test/config/` - Configuration parsing tests
- `src/test/reader/` - Data reader tests
- `src/test/writer/` - Data writer tests  
- `src/test/transformer/` - Data transformation tests
- `src/test/utils/` - Utility function tests
